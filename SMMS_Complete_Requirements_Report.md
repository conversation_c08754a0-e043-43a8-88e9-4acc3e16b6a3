# 🏥 School Medical Management System (SMMS) - Complete Requirements Report

## Contents
1. [Overview](#i-overview)
2. [Requirement Specifications](#ii-requirement-specifications)
3. [Design Specifications](#iii-design-specifications)
4. [Appendix](#iv-appendix)

---

## I. Overview

### 1. User Requirements

#### 1.1 Actors

| Actor | Description | Access Level |
|-------|-------------|--------------|
| **Admin** | System administrator with full access | Full system access |
| **Manager** | School management staff | High-level management functions |
| **Nurse** | School medical staff | Medical operations and student care |
| **Parent** | Student guardians | Limited access to their children's records |
| **Student** | Students using the system | Basic health information access |

#### 1.2 Use Cases

**Authentication & User Management:**
- UC-1: User Login System (Email/Password and Phone/OTP)
- UC-2: User Profile Management
- UC-3: Password Reset and Recovery

**Health Profile Management:**
- UC-4: Parent Health Profile Declaration
- UC-5: Medical History Management
- UC-6: Allergy and Chronic Disease Tracking
- UC-7: Vision and Hearing Records
- UC-8: Vaccination History Management

**Medical Operations:**
- UC-9: Medication Request and Management
- UC-10: Medical Event Handling (accidents, fever, falls, epidemics)
- UC-11: Medical Supply and Equipment Management
- UC-12: Emergency Response Procedures

**Health Activities:**
- UC-13: Vaccination Campaign Management
- UC-14: Periodic Health Check Management
- UC-15: Health Activity Consent Collection

**Communication & Information:**
- UC-16: School Information and Blog Management
- UC-17: Health Education Content Management
- UC-18: Parent-School Communication
- UC-19: Notification System

### 2. Overall Functionalities

#### 2.1 Screens Flow

```
🏠 PUBLIC AREA
├── Home Page (School Information, Health Documents, Blog)
├── Login/Registration
└── Health Education Resources

🔐 AUTHENTICATED AREA
├── 👨‍👩‍👧‍👦 PARENT PORTAL
│   ├── Dashboard
│   ├── Student Health Profile Declaration
│   │   ├── Basic Information
│   │   ├── Allergies & Chronic Diseases
│   │   ├── Medical History
│   │   ├── Vision & Hearing
│   │   └── Vaccination Records
│   ├── Medication Request
│   │   ├── Submit Medication
│   │   ├── Medication Instructions
│   │   └── Track Medication Status
│   ├── Health Activity Consent
│   │   ├── Vaccination Consent
│   │   └── Health Check Consent
│   ├── Health Reports & Results
│   └── Communication Center
│
├── 👩‍⚕️ MEDICAL STAFF PORTAL
│   ├── Dashboard
│   ├── Medical Event Management
│   │   ├── Record Medical Events
│   │   ├── Handle Emergencies
│   │   ├── Track Incidents
│   │   └── Generate Reports
│   ├── Medication Management
│   │   ├── Daily Medication Schedule
│   │   ├── Administer Medications
│   │   └── Track Medication Usage
│   ├── Medical Supply Management
│   │   ├── Inventory Tracking
│   │   ├── Stock Management
│   │   └── Supply Requests
│   ├── Vaccination Campaign
│   │   ├── Send Consent Forms
│   │   ├── Prepare Student Lists
│   │   ├── Record Vaccination
│   │   └── Post-Vaccination Monitoring
│   ├── Health Check Management
│   │   ├── Send Notification Forms
│   │   ├── Prepare Check Lists
│   │   ├── Conduct Health Checks
│   │   ├── Record Results
│   │   └── Schedule Consultations
│   └── Student Health Profiles
│
└── 👨‍💼 ADMIN PORTAL
    ├── User Management
    ├── System Configuration
    ├── Reports & Analytics
    └── Content Management (Blog, Documents)
```

#### 2.2 Screen Descriptions

**Public Screens:**
1. **Home Page**: School introduction, health education documents, blog posts, contact information
2. **Login Screen**: Multi-method authentication (email/password, phone/OTP)
3. **Registration**: New user account creation with role assignment
4. **Health Education**: Public access to health documents and educational materials

**Parent Portal Screens:**
1. **Parent Dashboard**: Overview of children's health status, recent activities, notifications
2. **Health Profile Declaration**: Comprehensive form for declaring student health information
3. **Medication Request**: Form to submit medications with detailed instructions
4. **Consent Management**: Digital consent forms for health activities
5. **Health Reports**: View health check results and vaccination records
6. **Communication Center**: Messages and notifications from school medical staff

**Medical Staff Screens:**
1. **Medical Dashboard**: Daily medication schedule, pending events, alerts
2. **Medical Event Recording**: Form to record and handle medical incidents
3. **Medication Administration**: Daily medication tracking and administration
4. **Supply Management**: Inventory management for medical supplies
5. **Vaccination Management**: Complete vaccination campaign workflow
6. **Health Check Management**: Periodic health examination workflow
7. **Student Health Profiles**: Comprehensive view of student health records

**Admin Screens:**
1. **Admin Dashboard**: System overview, user statistics, system health
2. **User Management**: CRUD operations for all user accounts
3. **Content Management**: Blog posts, health documents, school information
4. **System Reports**: Comprehensive reporting and analytics
5. **System Configuration**: System settings and configurations

#### 2.3 Screen Authorization

| Screen/Feature | Admin | Manager | Nurse | Parent | Student |
|----------------|-------|---------|-------|--------|---------|
| Home Page | ✓ | ✓ | ✓ | ✓ | ✓ |
| User Management | ✓ | ✗ | ✗ | ✗ | ✗ |
| Health Profile Declaration | ✓ | ✓ | ✓ | ✓ | ✗ |
| Medication Management | ✓ | ✓ | ✓ | ✓ | ✗ |
| Medical Event Handling | ✓ | ✓ | ✓ | ✗ | ✗ |
| Supply Management | ✓ | ✓ | ✓ | ✗ | ✗ |
| Vaccination Campaign | ✓ | ✓ | ✓ | ✓ | ✗ |
| Health Check Management | ✓ | ✓ | ✓ | ✓ | ✗ |
| Blog Management | ✓ | ✗ | ✗ | ✗ | ✗ |
| Reports & Analytics | ✓ | ✓ | ✓ | ✗ | ✗ |

#### 2.4 Non-UI Functions

**Background Services:**
- JWT Token Management and Validation
- Firebase Integration for OTP Services
- Email Notification Service
- Real-time SignalR Hub for notifications
- Redis Caching Service
- Cloudinary Image Upload Service
- Automated Health Check Reminders
- Vaccination Schedule Management
- Medical Event Alert System
- Data Import/Export Services

### 3. System High Level Design

#### 3.1 Database Design

**Core Entities:**
- **User**: System users with role-based access
- **Role**: User roles and permissions
- **Student**: Student information and relationships
- **SchoolClass**: Class organization and management
- **HealthProfile**: Comprehensive student health information
- **MedicalRequest**: Medication requests from parents
- **MedicalEvent**: Medical incidents and events
- **MedicalStock**: Medical inventory management
- **VaccinationCampaign**: Vaccination programs
- **HealthCheckCampaign**: Periodic health examinations
- **ActivityConsent**: Parent consent for health activities
- **Blog**: News and health education content
- **Notification**: System notifications

**Key Relationships:**
- User (1) → (N) Student (Parent-Child relationship)
- Student (N) → (1) SchoolClass
- Student (1) → (1) HealthProfile
- Student (1) → (N) MedicalRequest
- Student (1) → (N) MedicalEvent
- Student (1) → (N) ActivityConsent
- VaccinationCampaign (N) → (N) SchoolClass (Many-to-Many)
- HealthCheckCampaign (N) → (N) SchoolClass (Many-to-Many)

#### 3.2 Code Packages

**Backend Architecture (.NET 8):**
- **SMMS.API**: Controllers, authentication, middleware
- **SMMS.Application**: Business logic, services, DTOs
- **SMMS.Domain**: Entities, enums, business rules
- **SMMS.Infrastructure**: Database, repositories, external services

**Frontend Architecture (React + TypeScript):**
- **Components**: Reusable UI components
- **Pages**: Screen components and routing
- **Services**: API communication layer
- **Utils**: Helper functions and utilities
- **Types**: TypeScript type definitions

---

## II. Requirement Specifications

### 1. Authentication Feature

#### 1.1 UC-1_Login System

| Field | Value |
|-------|-------|
| **UC ID and UC-1 Login System Name** | UC-1_Login System |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | All Users |
| **Secondary Actor** | System |
| **Trigger** | User clicks Login button from any page requiring authentication |
| **Description** | As a user, I want to be able to log into the system so that I can use the system authentication features to access my personalized account |
| **Preconditions** | User account has been created & authorized |
| **Postconditions** | User logs in the system successfully |

**Normal Flow:**
1. User accesses the User Login screen
2. User enters the login details (email and password) or other login options
3. User clicks the Login button
4. System validates the login details
5. System allows user to access
6. System tracks user's successful login to the Activity Log
7. System redirects user to the Home Page for the previous page

**Alternative Flows:**
- **2.1 Google Login**: User chooses to login system using Google account
- **2.2 Phone/OTP Login**: User chooses to login using phone number and OTP verification

**Exceptions:**
- **3.0 E1 System can't authenticate the user**: Error message screen is shown to the user
- **3.1 User cancels the logging in**: UC stops, change to UC Reset Password

### 2. Health Profile Management Feature

#### 2.1 UC-4_Parent Health Profile Declaration

| Field | Value |
|-------|-------|
| **UC ID and Name** | UC-4_Parent Health Profile Declaration |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | Parent |
| **Secondary Actor** | Medical Staff |
| **Trigger** | Parent needs to declare or update student health information |
| **Description** | Parent declares comprehensive health information for their child including allergies, chronic diseases, medical history, vision, hearing, and vaccination records |
| **Preconditions** | Parent is logged in and has student registered in system |
| **Postconditions** | Student health profile is created/updated successfully |

**Normal Flow:**
1. Parent accesses Health Profile Declaration screen
2. System displays comprehensive health profile form
3. Parent fills in basic health information
4. Parent declares allergies and chronic diseases
5. Parent provides medical treatment history
6. Parent records vision and hearing information
7. Parent updates vaccination records
8. Parent submits the health profile
9. System validates and saves the information
10. System sends confirmation to parent
11. System notifies medical staff of profile update

**Alternative Flows:**
- **2.1 Partial Save**: Parent can save incomplete profile and continue later
- **2.2 Document Upload**: Parent can upload medical documents as supporting evidence

**Exceptions:**
- **3.0 E1 Validation Error**: System shows validation errors for incomplete or invalid data
- **3.1 System Error**: Error message shown, data is temporarily saved for recovery

### 3. Medical Operations Feature

#### 3.1 UC-9_Medication Request and Management

| Field | Value |
|-------|-------|
| **UC ID and Name** | UC-9_Medication Request and Management |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | Parent |
| **Secondary Actor** | Medical Staff |
| **Trigger** | Parent needs to send medication to school for student |
| **Description** | Parent submits medication request with detailed instructions for medical staff to administer to student during school hours |
| **Preconditions** | Parent is logged in, student is registered, health profile exists |
| **Postconditions** | Medication request is submitted and tracked in system |

**Normal Flow:**
1. Parent accesses Medication Request screen
2. Parent selects student from their children list
3. Parent fills medication details (name, dosage, frequency, duration)
4. Parent provides administration instructions and timing
5. Parent uploads prescription or doctor's note
6. Parent specifies special instructions or precautions
7. Parent submits medication request
8. System validates request information
9. System creates medication schedule
10. System notifies medical staff of new request
11. Medical staff reviews and approves request
12. System sends confirmation to parent

**Alternative Flows:**
- **2.1 Emergency Medication**: Fast-track process for emergency medications
- **2.2 Recurring Medication**: Setup for daily/weekly recurring medications

**Exceptions:**
- **3.0 E1 Invalid Medication**: System rejects medication not in approved list
- **3.1 Missing Documentation**: Request held pending required documentation

#### 3.2 UC-10_Medical Event Handling

| Field | Value |
|-------|-------|
| **UC ID and Name** | UC-10_Medical Event Handling |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | Medical Staff |
| **Secondary Actor** | Parent, Admin |
| **Trigger** | Medical event occurs (accident, fever, fall, epidemic, etc.) |
| **Description** | Medical staff records and handles medical events that occur during school hours, ensuring proper treatment and communication |
| **Preconditions** | Medical staff is logged in, student is identified |
| **Postconditions** | Medical event is recorded, treated, and communicated appropriately |

**Normal Flow:**
1. Medical staff accesses Medical Event Recording screen
2. Staff identifies student involved in event
3. Staff selects event type (accident, illness, injury, etc.)
4. Staff records event details, symptoms, and severity
5. Staff documents immediate treatment provided
6. Staff takes photos if necessary (with consent)
7. Staff determines if parent notification is required
8. System automatically notifies parent based on severity
9. Staff updates medical supplies used
10. Staff schedules follow-up if needed
11. System generates incident report
12. Staff completes event documentation

**Alternative Flows:**
- **2.1 Emergency Event**: Immediate parent and emergency contact notification
- **2.2 Epidemic Event**: Mass notification and health department reporting

**Exceptions:**
- **3.0 E1 Unable to Identify Student**: Manual identification process initiated
- **3.1 System Unavailable**: Paper backup process with later digital entry

### 4. Health Activities Feature

#### 4.1 UC-13_Vaccination Campaign Management

| Field | Value |
|-------|-------|
| **UC ID and Name** | UC-13_Vaccination Campaign Management |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | Medical Staff |
| **Secondary Actor** | Parent, Admin |
| **Trigger** | School initiates vaccination campaign |
| **Description** | Complete workflow for managing vaccination campaigns from consent collection to post-vaccination monitoring |
| **Preconditions** | Vaccination campaign is planned and approved |
| **Postconditions** | Vaccination campaign is completed with full documentation |

**Normal Flow:**
1. **Phase 1: Consent Collection**
   - Medical staff creates vaccination campaign
   - System generates consent forms for target classes
   - System sends consent notifications to parents
   - Parents review vaccination information
   - Parents provide digital consent or refusal
   - System tracks consent collection progress

2. **Phase 2: Preparation**
   - System generates list of consented students
   - Medical staff prepares vaccination schedule
   - System organizes students by class and time slots
   - Medical staff prepares vaccination supplies
   - System sends reminder notifications

3. **Phase 3: Vaccination**
   - Medical staff accesses daily vaccination list
   - Staff verifies student identity and consent
   - Staff administers vaccination
   - System records vaccination details (batch, time, staff)
   - Staff monitors for immediate reactions
   - System updates vaccination records

4. **Phase 4: Post-Vaccination Monitoring**
   - System sends post-vaccination care instructions to parents
   - Medical staff monitors students for adverse reactions
   - Parents report any side effects through system
   - Staff follows up on reported reactions
   - System generates vaccination completion report

**Alternative Flows:**
- **2.1 Partial Consent**: Handle students with partial or conditional consent
- **2.2 Medical Exemption**: Process medical exemption requests

**Exceptions:**
- **3.0 E1 Adverse Reaction**: Emergency response protocol activated
- **3.1 Supply Shortage**: Campaign postponement and rescheduling

#### 4.2 UC-14_Periodic Health Check Management

| Field | Value |
|-------|-------|
| **UC ID and Name** | UC-14_Periodic Health Check Management |
| **Created By** | Development Team |
| **Date Created** | 16 Jan 2024 |
| **Primary Actor** | Medical Staff |
| **Secondary Actor** | Parent, Admin |
| **Trigger** | Scheduled periodic health examination |
| **Description** | Complete workflow for conducting periodic health checks including notification, examination, and follow-up |
| **Preconditions** | Health check schedule is established |
| **Postconditions** | Health checks completed with results communicated to parents |

**Normal Flow:**
1. **Phase 1: Notification**
   - System generates health check notifications
   - System sends examination details to parents
   - Parents acknowledge receipt of notification
   - System tracks notification responses

2. **Phase 2: Preparation**
   - System creates examination schedule by class
   - Medical staff prepares examination equipment
   - System generates student examination lists
   - Staff reviews student health profiles

3. **Phase 3: Health Examination**
   - Medical staff conducts health examinations
   - Staff records examination results (vision, hearing, dental, physical)
   - System calculates BMI and growth metrics
   - Staff identifies any abnormal findings
   - System updates health profiles with results

4. **Phase 4: Results and Follow-up**
   - System generates health examination reports
   - System sends results to parents
   - Staff schedules individual consultations for abnormal findings
   - System tracks follow-up appointments
   - Staff provides health recommendations

**Alternative Flows:**
- **2.1 Absent Student**: Reschedule examination for absent students
- **2.2 Abnormal Finding**: Immediate parent notification and consultation scheduling

**Exceptions:**
- **3.0 E1 Equipment Malfunction**: Use backup equipment or reschedule
- **3.1 Student Refusal**: Document refusal and notify parent

---

## III. Design Specifications

### 1. Health Profile Management Feature

#### 1.1 Parent Health Profile Declaration

**a. Health Profile Declaration Screen**

**UI Design**
[This describes the UI layout (Mockup prototype) & descriptions for screen fields/components]

**<<Mockup prototype>>**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Basic Information** | Student Name | Text | Student's full name (read-only) |
| | Date of Birth | Date | Student's birth date (read-only) |
| | Blood Type | Dropdown | Student's blood type (A, B, AB, O, Unknown) |
| | Emergency Contact | Text | Primary emergency contact number |
| **Allergies & Chronic Diseases** | Food Allergies | Multi-select | List of food allergies with severity |
| | Drug Allergies | Multi-select | Medication allergies and reactions |
| | Environmental Allergies | Multi-select | Environmental allergens (dust, pollen, etc.) |
| | Chronic Diseases | Multi-select | Ongoing medical conditions |
| | Disease Details | Textarea | Detailed description of chronic conditions |
| **Medical History** | Previous Surgeries | Textarea | History of surgical procedures |
| | Hospitalizations | Textarea | Previous hospital admissions |
| | Current Medications | Textarea | Ongoing medication regimen |
| | Family Medical History | Textarea | Relevant family medical history |
| **Vision & Hearing** | Vision Status | Radio | Normal/Corrected/Impaired |
| | Vision Details | Textarea | Glasses/contacts prescription details |
| | Hearing Status | Radio | Normal/Impaired/Hearing Aid |
| | Hearing Details | Textarea | Hearing aid or impairment details |
| **Vaccination Records** | Vaccination List | Checklist | Standard vaccination checklist |
| | Vaccination Dates | Date inputs | Dates for each vaccination |
| | Vaccination Documents | File upload | Upload vaccination certificates |

**Database Access**
[Provide the design description for the screen/function to access the database here: what table the screen/function would access, which transactions does it make (C-Create, R-Read, U-Update, or D-Delete), and how/purpose of the access]

| Table | CRUD | Description |
|-------|------|-------------|
| Student | R | Read student basic information for profile display |
| HealthProfile | C, R, U | Create new or update existing health profile data |
| HealthAllergy | C, R, U, D | Manage student allergy records |
| HealthCondition | C, R, U, D | Manage chronic disease records |
| VaccinationRecord | C, R, U, D | Manage vaccination history |
| HealthDocument | C, R, D | Manage uploaded health documents |

**SQL Commands**
```sql
-- Read student information
SELECT s.Id, s.FullName, s.DateOfBirth, s.StudentCode
FROM Student s
WHERE s.Id = @StudentId AND s.DeletedTime IS NULL;

-- Create/Update health profile
INSERT INTO HealthProfile (Id, StudentId, BloodType, EmergencyContact, VisionStatus, HearingStatus, CreatedTime, CreatedBy)
VALUES (@Id, @StudentId, @BloodType, @EmergencyContact, @VisionStatus, @HearingStatus, GETDATE(), @UserId);

-- Insert allergy records
INSERT INTO HealthAllergy (Id, StudentId, AllergyType, AllergyName, Severity, CreatedTime, CreatedBy)
VALUES (@Id, @StudentId, @AllergyType, @AllergyName, @Severity, GETDATE(), @UserId);

-- Update vaccination records
UPDATE VaccinationRecord
SET VaccinationDate = @VaccinationDate, UpdatedTime = GETDATE(), UpdatedBy = @UserId
WHERE StudentId = @StudentId AND VaccinationType = @VaccinationType;
```

### 2. Medical Operations Feature

#### 2.1 Medication Request Management

**a. Medication Request Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Student Selection** | Student Name | Dropdown | Select from parent's children |
| | Student Info | Display | Show selected student's basic info |
| **Medication Details** | Medication Name | Text | Name of medication |
| | Medication Type | Dropdown | Tablet, Liquid, Inhaler, etc. |
| | Dosage | Text | Dosage amount (e.g., "5ml", "1 tablet") |
| | Frequency | Dropdown | How often (Daily, Twice daily, etc.) |
| | Administration Times | Time inputs | Specific times to give medication |
| | Duration | Date range | Start and end dates |
| **Instructions** | Administration Method | Dropdown | With food, before meals, etc. |
| | Special Instructions | Textarea | Additional instructions for staff |
| | Side Effects | Textarea | Known side effects to watch for |
| | Emergency Instructions | Textarea | What to do in case of reaction |
| **Documentation** | Prescription Upload | File upload | Doctor's prescription or note |
| | Medication Photo | File upload | Photo of medication packaging |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| Student | R | Read student information for selection |
| MedicalRequest | C, R, U | Create and manage medication requests |
| MedicationSchedule | C, R, U | Create administration schedule |
| MedicalDocument | C, R | Store uploaded prescription documents |
| Notification | C | Create notifications for medical staff |

**SQL Commands**
```sql
-- Create medication request
INSERT INTO MedicalRequest (Id, StudentId, ParentId, MedicationName, Dosage, Frequency,
    StartDate, EndDate, Instructions, Status, CreatedTime, CreatedBy)
VALUES (@Id, @StudentId, @ParentId, @MedicationName, @Dosage, @Frequency,
    @StartDate, @EndDate, @Instructions, 'Pending', GETDATE(), @UserId);

-- Create medication schedule
INSERT INTO MedicationSchedule (Id, MedicalRequestId, ScheduledTime, Status, CreatedTime)
VALUES (@Id, @MedicalRequestId, @ScheduledTime, 'Scheduled', GETDATE());

-- Notify medical staff
INSERT INTO Notification (Id, UserId, Title, Message, Type, CreatedTime)
SELECT NEWID(), u.Id, 'New Medication Request',
    'New medication request for student: ' + s.FullName, 'MedicationRequest', GETDATE()
FROM [User] u
JOIN Role r ON u.RoleId = r.Id
JOIN Student s ON s.Id = @StudentId
WHERE r.RoleName IN ('Nurse', 'Manager', 'Admin');
```

#### 2.2 Medical Event Recording

**a. Medical Event Recording Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Event Information** | Event Date/Time | DateTime | When the event occurred |
| | Student Name | Search/Select | Find and select affected student |
| | Event Type | Dropdown | Accident, Illness, Injury, Fall, etc. |
| | Event Location | Dropdown | Classroom, Playground, Cafeteria, etc. |
| | Severity Level | Radio | Low, Medium, High, Critical |
| **Event Details** | Event Description | Textarea | Detailed description of what happened |
| | Symptoms Observed | Checklist | List of observed symptoms |
| | Witness Information | Textarea | Names and details of witnesses |
| | Photos | File upload | Photos of injury or incident (if appropriate) |
| **Treatment Provided** | Immediate Treatment | Textarea | First aid or treatment given |
| | Medications Given | Multi-select | Any medications administered |
| | Medical Supplies Used | Multi-select | Supplies used from inventory |
| | Treatment Staff | Text | Name of staff who provided treatment |
| **Follow-up Actions** | Parent Notification | Radio | Yes/No - was parent notified |
| | Notification Method | Dropdown | Phone, SMS, Email |
| | Further Treatment Needed | Radio | Yes/No |
| | Follow-up Instructions | Textarea | Instructions for ongoing care |
| | Return to Class | Radio | Yes/No - can student return to class |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| Student | R | Read student information |
| MedicalEvent | C, R, U | Create and manage medical event records |
| MedicalTreatment | C, R | Record treatment provided |
| MedicalSupplyUsage | C, R | Track medical supplies used |
| EventDocument | C, R | Store photos and documents |
| ParentNotification | C, R | Record parent notifications |

**SQL Commands**
```sql
-- Create medical event record
INSERT INTO MedicalEvent (Id, StudentId, EventType, EventDateTime, Location,
    Severity, Description, TreatmentProvided, StaffId, CreatedTime, CreatedBy)
VALUES (@Id, @StudentId, @EventType, @EventDateTime, @Location,
    @Severity, @Description, @TreatmentProvided, @StaffId, GETDATE(), @UserId);

-- Record medical supplies used
INSERT INTO MedicalSupplyUsage (Id, MedicalEventId, SupplyId, QuantityUsed, UsedBy, UsedTime)
VALUES (@Id, @MedicalEventId, @SupplyId, @QuantityUsed, @UserId, GETDATE());

-- Update supply inventory
UPDATE MedicalSupply
SET CurrentStock = CurrentStock - @QuantityUsed,
    LastUpdated = GETDATE(),
    UpdatedBy = @UserId
WHERE Id = @SupplyId;

-- Create parent notification
INSERT INTO ParentNotification (Id, MedicalEventId, ParentId, NotificationMethod,
    NotificationTime, Message, Status)
VALUES (@Id, @MedicalEventId, @ParentId, @NotificationMethod,
    GETDATE(), @Message, 'Sent');
```

### 3. Health Activities Feature

#### 3.1 Vaccination Campaign Management

**a. Vaccination Campaign Creation Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Campaign Information** | Campaign Name | Text | Name of vaccination campaign |
| | Vaccination Type | Dropdown | Type of vaccine (COVID-19, Flu, etc.) |
| | Campaign Description | Textarea | Detailed description and purpose |
| | Target Classes | Multi-select | Classes included in campaign |
| | Campaign Date Range | Date range | Start and end dates for campaign |
| **Consent Management** | Consent Deadline | Date | Deadline for parent consent |
| | Consent Form Template | File upload | Template for consent form |
| | Information Document | File upload | Vaccination information for parents |
| **Scheduling** | Vaccination Dates | Date inputs | Specific dates for each class |
| | Time Slots | Time inputs | Time slots for vaccination |
| | Medical Staff Assigned | Multi-select | Staff assigned to campaign |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| VaccinationCampaign | C, R, U | Create and manage vaccination campaigns |
| CampaignClass | C, R, D | Link campaigns to target classes |
| ConsentForm | C, R, U | Manage consent forms |
| VaccinationSchedule | C, R, U | Schedule vaccination appointments |
| CampaignDocument | C, R | Store campaign documents |

**SQL Commands**
```sql
-- Create vaccination campaign
INSERT INTO VaccinationCampaign (Id, CampaignName, VaccinationType, Description,
    StartDate, EndDate, ConsentDeadline, Status, CreatedTime, CreatedBy)
VALUES (@Id, @CampaignName, @VaccinationType, @Description,
    @StartDate, @EndDate, @ConsentDeadline, 'Planning', GETDATE(), @UserId);

-- Link campaign to classes
INSERT INTO CampaignClass (Id, CampaignId, ClassId, ScheduledDate, ScheduledTime)
VALUES (@Id, @CampaignId, @ClassId, @ScheduledDate, @ScheduledTime);

-- Generate consent forms for all students in target classes
INSERT INTO ConsentForm (Id, CampaignId, StudentId, ParentId, Status, CreatedTime)
SELECT NEWID(), @CampaignId, s.Id, s.ParentId, 'Pending', GETDATE()
FROM Student s
JOIN CampaignClass cc ON s.ClassId = cc.ClassId
WHERE cc.CampaignId = @CampaignId AND s.DeletedTime IS NULL;
```

**b. Vaccination Administration Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Student Information** | Student List | Table | List of students scheduled for vaccination |
| | Student Name | Display | Student's full name |
| | Class | Display | Student's class |
| | Consent Status | Display | Parent consent status |
| | Health Alerts | Display | Any health alerts or allergies |
| **Vaccination Details** | Vaccine Batch Number | Text | Batch number of vaccine used |
| | Vaccination Time | DateTime | Actual time of vaccination |
| | Vaccination Site | Dropdown | Left arm, right arm, etc. |
| | Administering Staff | Dropdown | Staff member giving vaccination |
| **Post-Vaccination** | Immediate Reaction | Radio | Yes/No - any immediate reaction |
| | Reaction Details | Textarea | Details of any reaction observed |
| | Observation Period | Timer | 15-minute observation countdown |
| | Student Status | Radio | Normal/Needs attention/Emergency |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| VaccinationRecord | C, R, U | Record vaccination administration |
| ConsentForm | R, U | Check consent and update status |
| VaccinationReaction | C, R | Record any adverse reactions |
| MedicalSupply | R, U | Track vaccine inventory usage |

**SQL Commands**
```sql
-- Record vaccination administration
INSERT INTO VaccinationRecord (Id, StudentId, CampaignId, VaccinationType,
    BatchNumber, VaccinationDate, VaccinationSite, AdministeredBy, Status)
VALUES (@Id, @StudentId, @CampaignId, @VaccinationType,
    @BatchNumber, GETDATE(), @VaccinationSite, @StaffId, 'Completed');

-- Update consent form status
UPDATE ConsentForm
SET Status = 'Vaccinated',
    VaccinationDate = GETDATE(),
    UpdatedTime = GETDATE()
WHERE StudentId = @StudentId AND CampaignId = @CampaignId;

-- Record any adverse reaction
INSERT INTO VaccinationReaction (Id, VaccinationRecordId, ReactionType,
    ReactionSeverity, ReactionTime, TreatmentProvided, ReportedBy)
VALUES (@Id, @VaccinationRecordId, @ReactionType,
    @ReactionSeverity, GETDATE(), @TreatmentProvided, @StaffId);
```

#### 3.2 Health Check Campaign Management

**a. Health Check Campaign Creation Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Campaign Information** | Campaign Name | Text | Name of health check campaign |
| | Check Type | Dropdown | Annual, Dental, Vision, Comprehensive |
| | Target Classes | Multi-select | Classes to be examined |
| | Examination Period | Date range | Duration of health checks |
| **Examination Details** | Check Components | Checklist | Height, Weight, Vision, Hearing, Dental |
| | Equipment Required | Checklist | Medical equipment needed |
| | Staff Requirements | Number input | Number of medical staff needed |
| | External Providers | Text | Any external medical providers |
| **Scheduling** | Examination Schedule | Calendar | Schedule by class and date |
| | Time Allocation | Time input | Time per student examination |
| | Preparation Time | Time input | Setup and cleanup time |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| HealthCheckCampaign | C, R, U | Create and manage health check campaigns |
| CampaignClass | C, R, D | Link campaigns to classes |
| HealthCheckSchedule | C, R, U | Schedule health examinations |
| HealthCheckComponent | C, R | Define examination components |

**SQL Commands**
```sql
-- Create health check campaign
INSERT INTO HealthCheckCampaign (Id, CampaignName, CheckType, Description,
    StartDate, EndDate, Status, CreatedTime, CreatedBy)
VALUES (@Id, @CampaignName, @CheckType, @Description,
    @StartDate, @EndDate, 'Planning', GETDATE(), @UserId);

-- Schedule health checks for students
INSERT INTO HealthCheckSchedule (Id, CampaignId, StudentId, ScheduledDate,
    ScheduledTime, Status, CreatedTime)
SELECT NEWID(), @CampaignId, s.Id, @ScheduledDate,
    @ScheduledTime, 'Scheduled', GETDATE()
FROM Student s
JOIN CampaignClass cc ON s.ClassId = cc.ClassId
WHERE cc.CampaignId = @CampaignId AND s.DeletedTime IS NULL;
```

**b. Health Examination Recording Screen**

**UI Design**

| Field Group Name | Field Name | Field Type | Description |
|------------------|------------|------------|-------------|
| **Student Information** | Student Name | Display | Student being examined |
| | Date of Birth | Display | Student's age calculation |
| | Previous Records | Link | Link to previous health records |
| **Physical Measurements** | Height | Number | Height in centimeters |
| | Weight | Number | Weight in kilograms |
| | BMI | Calculated | Auto-calculated BMI |
| | BMI Status | Display | Underweight/Normal/Overweight/Obese |
| **Vision Testing** | Right Eye Vision | Dropdown | 20/20, 20/30, etc. |
| | Left Eye Vision | Dropdown | Vision test results |
| | Color Vision | Radio | Normal/Deficient |
| | Vision Notes | Textarea | Additional observations |
| **Hearing Testing** | Right Ear | Radio | Normal/Impaired |
| | Left Ear | Radio | Hearing test results |
| | Hearing Notes | Textarea | Additional observations |
| **Dental Examination** | Dental Status | Radio | Good/Fair/Poor |
| | Cavities Count | Number | Number of cavities found |
| | Dental Notes | Textarea | Dental examination notes |
| **General Health** | Overall Assessment | Radio | Excellent/Good/Fair/Poor |
| | Abnormal Findings | Textarea | Any abnormal findings |
| | Recommendations | Textarea | Health recommendations |
| | Follow-up Required | Radio | Yes/No |

**Database Access**

| Table | CRUD | Description |
|-------|------|-------------|
| HealthCheckRecord | C, R, U | Record health examination results |
| HealthMeasurement | C, R, U | Store physical measurements |
| HealthCheckSchedule | R, U | Update examination status |
| HealthRecommendation | C, R | Store health recommendations |
| ParentNotification | C | Notify parents of results |

**SQL Commands**
```sql
-- Record health check results
INSERT INTO HealthCheckRecord (Id, StudentId, CampaignId, ExaminationDate,
    Height, Weight, BMI, BMIStatus, OverallAssessment, ExaminedBy, CreatedTime)
VALUES (@Id, @StudentId, @CampaignId, GETDATE(),
    @Height, @Weight, @BMI, @BMIStatus, @OverallAssessment, @StaffId, GETDATE());

-- Record vision test results
INSERT INTO HealthMeasurement (Id, HealthCheckRecordId, MeasurementType,
    MeasurementValue, Notes, CreatedTime)
VALUES (@Id, @HealthCheckRecordId, 'Vision',
    @VisionResults, @VisionNotes, GETDATE());

-- Create follow-up recommendation if needed
INSERT INTO HealthRecommendation (Id, HealthCheckRecordId, RecommendationType,
    RecommendationText, Priority, CreatedTime, CreatedBy)
VALUES (@Id, @HealthCheckRecordId, @RecommendationType,
    @RecommendationText, @Priority, GETDATE(), @StaffId);

-- Notify parent of results
INSERT INTO ParentNotification (Id, StudentId, NotificationType, Title, Message,
    NotificationMethod, Status, CreatedTime)
VALUES (@Id, @StudentId, 'HealthCheckResult', 'Health Check Results Available',
    @ResultMessage, 'Email', 'Pending', GETDATE());
```

---

## IV. Appendix

### A. Functional Description Template

| Field | Description |
|-------|-------------|
| **UC ID and Name** | Unique identifier and descriptive name |
| **Created By** | Development team member |
| **Date Created** | Creation date |
| **Primary Actor** | Main user of the function |
| **Secondary Actor** | Supporting users |
| **Trigger** | Event that initiates the use case |
| **Description** | Detailed description of the functionality |
| **Preconditions** | Required conditions before execution |
| **Postconditions** | Expected state after execution |
| **Normal Flow** | Step-by-step normal execution |
| **Alternative Flows** | Alternative execution paths |
| **Exceptions** | Error conditions and handling |
| **Priority** | High/Medium/Low priority |
| **Frequency of Use** | How often the function is used |
| **Business Rules** | Business constraints and rules |
| **Other Information** | Additional relevant information |
| **Assumptions** | Assumptions made during design |

### B. UI Design Mockup Prototype & Descriptions

**Field Group Structure:**
- **Field Group Name**: Logical grouping of related fields
- **Field Name**: Individual field identifier
- **Field Type**: Input type (Text, Dropdown, Radio, etc.)
- **Description**: Purpose and data initialization design

### C. Database Access Design

**Table Access Pattern:**
- **Table Name**: Database table being accessed
- **CRUD Operations**: Create, Read, Update, Delete operations
- **Description**: Purpose and method of database access
- **Transaction Details**: Specific database transactions
- **SQL Commands**: Detailed SQL implementation

### D. System Architecture Notes

**Technology Stack:**
- **Backend**: .NET 8 Web API with Entity Framework Core
- **Frontend**: React 18 with TypeScript and Material-UI
- **Database**: SQL Server 2022
- **Authentication**: JWT with refresh tokens
- **Real-time**: SignalR for notifications
- **File Storage**: Cloudinary for images and documents
- **Caching**: Redis for performance optimization
- **Email**: SendGrid for email notifications
- **SMS/OTP**: Firebase for phone verification

**Security Considerations:**
- Role-based access control (RBAC)
- Data encryption at rest and in transit
- HIPAA compliance for health data
- Audit logging for all medical operations
- Secure file upload with virus scanning
- Rate limiting for API endpoints
- Input validation and sanitization

**Performance Requirements:**
- Response time < 2 seconds for most operations
- Support for 1000+ concurrent users
- 99.9% uptime availability
- Automated backup and disaster recovery
- Scalable architecture for future growth

---

*This document serves as the complete requirements specification for the School Medical Management System (SMMS), providing detailed functional requirements, use cases, UI designs, and database specifications for all major features of the system.*
